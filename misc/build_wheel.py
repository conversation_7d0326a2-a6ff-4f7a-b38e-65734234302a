"""
The main GitHub workflow where wheels are built:
https://github.com/mypyc/mypy_mypyc-wheels/blob/master/.github/workflows/build.yml

The script that builds wheels:
https://github.com/mypyc/mypy_mypyc-wheels/blob/master/build_wheel.py

That script is a light wrapper around cibuildwheel. Now that cibuildwheel has native configuration
and better support for local builds, we could probably replace the script.
"""

raise ImportError("This script has been moved back to https://github.com/mypyc/mypy_mypyc-wheels")
