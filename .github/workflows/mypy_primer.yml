name: Run mypy_primer

on:
  # Only run on PR, since we diff against master
  pull_request:
    paths-ignore:
    - 'docs/**'
    - '**/*.rst'
    - '**/*.md'
    - 'misc/**'
    - 'mypyc/**'
    - 'mypy/stubtest.py'
    - 'mypy/stubgen.py'
    - 'mypy/stubgenc.py'
    - 'mypy/test/**'
    - 'test-data/**'

permissions:
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  mypy_primer:
    name: Run mypy_primer
    runs-on: ubuntu-latest
    strategy:
      matrix:
        shard-index: [0, 1, 2, 3, 4, 5]
      fail-fast: false
    timeout-minutes: 60
    steps:
      - uses: actions/checkout@v4
        with:
          path: mypy_to_test
          fetch-depth: 0
          persist-credentials: false
      - uses: actions/setup-python@v5
        with:
          python-version: "3.13"
      - name: Install dependencies
        run: |
          python -m pip install -U pip
          pip install git+https://github.com/hauntsaninja/mypy_primer.git
      - name: Run mypy_primer
        shell: bash
        run: |
          cd mypy_to_test
          echo "new commit"
          git rev-list --format=%s --max-count=1 $GITHUB_SHA

          MERGE_BASE=$(git merge-base $GITHUB_SHA origin/$GITHUB_BASE_REF)
          git checkout -b base_commit $MERGE_BASE
          echo "base commit"
          git rev-list --format=%s --max-count=1 base_commit

          echo ''
          cd ..
          # fail action if exit code isn't zero or one
          (
            mypy_primer \
            --repo mypy_to_test \
            --new $GITHUB_SHA --old base_commit \
            --num-shards 6 --shard-index ${{ matrix.shard-index }} \
            --debug \
            --additional-flags="--debug-serialize" \
            --output concise \
            | tee diff_${{ matrix.shard-index }}.txt
          ) || [ $? -eq 1 ]
      - if: ${{ matrix.shard-index == 0 }}
        name: Save PR number
        run: |
          echo ${{ github.event.pull_request.number }} | tee pr_number.txt
      - name: Upload mypy_primer diff + PR number
        uses: actions/upload-artifact@v4
        if: ${{ matrix.shard-index == 0 }}
        with:
          name: mypy_primer_diffs-${{ matrix.shard-index }}
          path: |
            diff_${{ matrix.shard-index }}.txt
            pr_number.txt
      - name: Upload mypy_primer diff
        uses: actions/upload-artifact@v4
        if: ${{ matrix.shard-index != 0 }}
        with:
          name: mypy_primer_diffs-${{ matrix.shard-index }}
          path: diff_${{ matrix.shard-index }}.txt

  join_artifacts:
    name: Join artifacts
    runs-on: ubuntu-latest
    needs: [mypy_primer]
    steps:
      - name: Merge artifacts
        uses: actions/upload-artifact/merge@v4
        with:
          name: mypy_primer_diffs
          pattern: mypy_primer_diffs-*
          delete-merged: true
